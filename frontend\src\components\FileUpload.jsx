import React, { useState, useRef } from 'react';
import { uploadAPI } from '../utils/api';

const FileUpload = ({ 
  type = 'image', 
  multiple = false, 
  onUploadSuccess, 
  onUploadError,
  accept,
  maxSize = 10 * 1024 * 1024 // 10MB default
}) => {
  const [isDragging, setIsDragging] = useState(false);
  const [isUploading, setIsUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState(0);
  const [uploadedFiles, setUploadedFiles] = useState([]);
  const fileInputRef = useRef(null);

  const getAcceptedTypes = () => {
    if (accept) return accept;
    
    switch (type) {
      case 'image':
        return 'image/jpeg,image/jpg,image/png,image/gif,image/webp';
      case 'resume':
        return 'application/pdf,.doc,.docx';
      default:
        return '*/*';
    }
  };

  const validateFile = (file) => {
    // Check file size
    if (file.size > maxSize) {
      throw new Error(`File size must be less than ${Math.round(maxSize / 1024 / 1024)}MB`);
    }

    // Check file type
    const acceptedTypes = getAcceptedTypes().split(',');
    const isValidType = acceptedTypes.some(acceptedType => {
      if (acceptedType.startsWith('.')) {
        return file.name.toLowerCase().endsWith(acceptedType.toLowerCase());
      }
      return file.type === acceptedType.trim();
    });

    if (!isValidType) {
      throw new Error(`File type not supported. Accepted types: ${getAcceptedTypes()}`);
    }

    return true;
  };

  const handleFileSelect = (files) => {
    const fileList = Array.from(files);
    
    try {
      // Validate all files first
      fileList.forEach(validateFile);
      
      // Upload files
      uploadFiles(fileList);
    } catch (error) {
      if (onUploadError) {
        onUploadError(error.message);
      }
    }
  };

  const uploadFiles = async (files) => {
    setIsUploading(true);
    setUploadProgress(0);

    try {
      let response;
      
      if (multiple && files.length > 1) {
        // Upload multiple images
        response = await uploadAPI.uploadImages(files);
      } else {
        // Upload single file
        const file = files[0];
        if (type === 'resume') {
          response = await uploadAPI.uploadResume(file);
        } else {
          response = await uploadAPI.uploadImage(file);
        }
      }

      if (response.success) {
        const uploadedData = Array.isArray(response.data) ? response.data : [response.data];
        setUploadedFiles(prev => [...prev, ...uploadedData]);
        
        if (onUploadSuccess) {
          onUploadSuccess(response.data);
        }
      }
    } catch (error) {
      console.error('Upload error:', error);
      if (onUploadError) {
        onUploadError(error);
      }
    } finally {
      setIsUploading(false);
      setUploadProgress(0);
    }
  };

  const handleDragOver = (e) => {
    e.preventDefault();
    setIsDragging(true);
  };

  const handleDragLeave = (e) => {
    e.preventDefault();
    setIsDragging(false);
  };

  const handleDrop = (e) => {
    e.preventDefault();
    setIsDragging(false);
    
    const files = e.dataTransfer.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  };

  const handleInputChange = (e) => {
    const files = e.target.files;
    if (files.length > 0) {
      handleFileSelect(files);
    }
  };

  const removeFile = (index) => {
    setUploadedFiles(prev => prev.filter((_, i) => i !== index));
  };

  const formatFileSize = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  return (
    <div className="file-upload">
      <div
        className={`upload-area ${isDragging ? 'dragging' : ''} ${isUploading ? 'uploading' : ''}`}
        onDragOver={handleDragOver}
        onDragLeave={handleDragLeave}
        onDrop={handleDrop}
        onClick={() => fileInputRef.current?.click()}
      >
        <input
          ref={fileInputRef}
          type="file"
          accept={getAcceptedTypes()}
          multiple={multiple}
          onChange={handleInputChange}
          style={{ display: 'none' }}
        />

        <div className="upload-content">
          {isUploading ? (
            <div className="upload-progress">
              <div className="loading"></div>
              <p>Uploading files...</p>
              {uploadProgress > 0 && (
                <div className="progress-bar">
                  <div 
                    className="progress-fill" 
                    style={{ width: `${uploadProgress}%` }}
                  ></div>
                </div>
              )}
            </div>
          ) : (
            <>
              <div className="upload-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21 15v4a2 2 0 01-2 2H5a2 2 0 01-2-2v-4"></path>
                  <polyline points="7,10 12,15 17,10"></polyline>
                  <line x1="12" y1="15" x2="12" y2="3"></line>
                </svg>
              </div>
              <p className="upload-text">
                {isDragging 
                  ? 'Drop files here' 
                  : `Drag & drop ${type === 'image' ? 'images' : 'files'} here or click to browse`
                }
              </p>
              <p className="upload-hint">
                Max size: {Math.round(maxSize / 1024 / 1024)}MB
                {multiple && ' • Multiple files supported'}
              </p>
            </>
          )}
        </div>
      </div>

      {uploadedFiles.length > 0 && (
        <div className="uploaded-files">
          <h4>Uploaded Files</h4>
          <div className="file-list">
            {uploadedFiles.map((file, index) => (
              <div key={index} className="file-item">
                <div className="file-info">
                  <span className="file-name">{file.originalName}</span>
                  <span className="file-size">{formatFileSize(file.size)}</span>
                </div>
                <div className="file-actions">
                  <a 
                    href={`http://localhost:5000${file.path}`} 
                    target="_blank" 
                    rel="noopener noreferrer"
                    className="btn btn-sm btn-outline"
                  >
                    View
                  </a>
                  <button
                    className="btn btn-sm btn-danger"
                    onClick={() => removeFile(index)}
                  >
                    Remove
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}

      <style jsx>{`
        .file-upload {
          width: 100%;
        }

        .upload-area {
          border: 2px dashed var(--gray-300);
          border-radius: var(--radius-lg);
          padding: var(--spacing-3xl);
          text-align: center;
          cursor: pointer;
          transition: all var(--transition-normal);
          background-color: var(--gray-50);
        }

        .upload-area:hover {
          border-color: var(--primary-color);
          background-color: var(--primary-alpha);
        }

        .upload-area.dragging {
          border-color: var(--primary-color);
          background-color: var(--primary-alpha);
          transform: scale(1.02);
        }

        .upload-area.uploading {
          pointer-events: none;
          opacity: 0.7;
        }

        .upload-content {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: var(--spacing-md);
        }

        .upload-icon {
          color: var(--gray-400);
        }

        .upload-area:hover .upload-icon,
        .upload-area.dragging .upload-icon {
          color: var(--primary-color);
        }

        .upload-text {
          font-size: var(--font-size-lg);
          font-weight: var(--font-weight-medium);
          color: var(--text-color);
          margin: 0;
        }

        .upload-hint {
          font-size: var(--font-size-sm);
          color: var(--gray-500);
          margin: 0;
        }

        .upload-progress {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: var(--spacing-md);
        }

        .progress-bar {
          width: 200px;
          height: 4px;
          background-color: var(--gray-200);
          border-radius: 2px;
          overflow: hidden;
        }

        .progress-fill {
          height: 100%;
          background-color: var(--primary-color);
          transition: width var(--transition-normal);
        }

        .uploaded-files {
          margin-top: var(--spacing-xl);
        }

        .uploaded-files h4 {
          margin-bottom: var(--spacing-md);
          color: var(--text-color);
        }

        .file-list {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-sm);
        }

        .file-item {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--spacing-md);
          background-color: var(--background-color);
          border: 1px solid var(--gray-200);
          border-radius: var(--radius-md);
        }

        .file-info {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-xs);
        }

        .file-name {
          font-weight: var(--font-weight-medium);
          color: var(--text-color);
        }

        .file-size {
          font-size: var(--font-size-sm);
          color: var(--gray-500);
        }

        .file-actions {
          display: flex;
          gap: var(--spacing-sm);
        }

        .btn-outline {
          background-color: transparent;
          color: var(--primary-color);
          border: 1px solid var(--primary-color);
        }

        .btn-outline:hover {
          background-color: var(--primary-color);
          color: white;
        }

        .btn-danger {
          background-color: var(--error-color);
          color: white;
        }

        .btn-danger:hover {
          background-color: #dc2626;
        }

        @media (max-width: 768px) {
          .upload-area {
            padding: var(--spacing-2xl);
          }

          .file-item {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--spacing-md);
          }

          .file-actions {
            width: 100%;
            justify-content: flex-end;
          }
        }
      `}</style>
    </div>
  );
};

export default FileUpload;
