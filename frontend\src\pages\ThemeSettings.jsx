import React, { useState, useEffect } from 'react';
import { settingsAPI, themeUtils } from '../utils/api';

const ThemeSettings = () => {
  const [theme, setTheme] = useState({
    primaryColor: '#0066ff',
    backgroundColor: '#ffffff',
    textColor: '#222222',
    fontFamily: 'Poppins',
    fontSize: '16px'
  });
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [message, setMessage] = useState('');

  // Load current theme on component mount
  useEffect(() => {
    loadCurrentTheme();
  }, []);

  const loadCurrentTheme = async () => {
    try {
      const response = await settingsAPI.getTheme();
      if (response.success && response.data.theme) {
        setTheme(response.data.theme);
      }
    } catch (error) {
      console.error('Error loading theme:', error);
      setMessage('Error loading current theme settings');
    } finally {
      setIsLoading(false);
    }
  };

  // Handle input changes and apply theme in real-time
  const handleThemeChange = (key, value) => {
    const newTheme = { ...theme, [key]: value };
    setTheme(newTheme);

    // Apply theme immediately for live preview
    themeUtils.applyTheme(newTheme);
  };

  // Save theme to backend
  const handleSaveTheme = async () => {
    setIsSaving(true);
    setMessage('');

    try {
      const response = await settingsAPI.updateTheme(theme);
      if (response.success) {
        setMessage('Theme saved successfully!');
        setTimeout(() => setMessage(''), 3000);
      }
    } catch (error) {
      console.error('Error saving theme:', error);
      setMessage('Error saving theme. Please try again.');
    } finally {
      setIsSaving(false);
    }
  };

  // Reset to default theme
  const handleResetTheme = () => {
    const defaultTheme = {
      primaryColor: '#0066ff',
      backgroundColor: '#ffffff',
      textColor: '#222222',
      fontFamily: 'Poppins',
      fontSize: '16px'
    };
    setTheme(defaultTheme);
    themeUtils.applyTheme(defaultTheme);
  };

  if (isLoading) {
    return (
      <div className="theme-settings">
        <section className="section">
          <div className="container">
            <div className="loading-container">
              <div className="loading"></div>
              <p>Loading theme settings...</p>
            </div>
          </div>
        </section>
      </div>
    );
  }

  return (
    <div className="theme-settings">
      <section className="section">
        <div className="container">
          <h1 className="section-title">Theme Settings</h1>
          <p className="section-subtitle">
            Customize the appearance of your portfolio with live preview.
          </p>

          {message && (
            <div className={`message ${message.includes('Error') ? 'error' : 'success'}`}>
              {message}
            </div>
          )}

          <div className="theme-content">
            <div className="theme-form">
              <h3>Color Settings</h3>
              <div className="form-group">
                <label htmlFor="primaryColor">Primary Color</label>
                <input
                  type="color"
                  id="primaryColor"
                  value={theme.primaryColor}
                  onChange={(e) => handleThemeChange('primaryColor', e.target.value)}
                />
                <span className="color-value">{theme.primaryColor}</span>
              </div>

              <div className="form-group">
                <label htmlFor="backgroundColor">Background Color</label>
                <input
                  type="color"
                  id="backgroundColor"
                  value={theme.backgroundColor}
                  onChange={(e) => handleThemeChange('backgroundColor', e.target.value)}
                />
                <span className="color-value">{theme.backgroundColor}</span>
              </div>

              <div className="form-group">
                <label htmlFor="textColor">Text Color</label>
                <input
                  type="color"
                  id="textColor"
                  value={theme.textColor}
                  onChange={(e) => handleThemeChange('textColor', e.target.value)}
                />
                <span className="color-value">{theme.textColor}</span>
              </div>

              <h3>Typography</h3>
              <div className="form-group">
                <label htmlFor="fontFamily">Font Family</label>
                <select
                  id="fontFamily"
                  value={theme.fontFamily}
                  onChange={(e) => handleThemeChange('fontFamily', e.target.value)}
                >
                  <option value="Poppins">Poppins</option>
                  <option value="Inter">Inter</option>
                  <option value="Roboto">Roboto</option>
                  <option value="Open Sans">Open Sans</option>
                  <option value="Montserrat">Montserrat</option>
                  <option value="Lato">Lato</option>
                </select>
              </div>

              <div className="form-group">
                <label htmlFor="fontSize">Base Font Size</label>
                <select
                  id="fontSize"
                  value={theme.fontSize}
                  onChange={(e) => handleThemeChange('fontSize', e.target.value)}
                >
                  <option value="14px">Small (14px)</option>
                  <option value="16px">Medium (16px)</option>
                  <option value="18px">Large (18px)</option>
                  <option value="20px">Extra Large (20px)</option>
                </select>
              </div>

              <div className="form-actions">
                <button
                  className="btn btn-primary"
                  onClick={handleSaveTheme}
                  disabled={isSaving}
                >
                  {isSaving ? 'Saving...' : 'Save Changes'}
                </button>
                <button
                  className="btn btn-secondary"
                  onClick={handleResetTheme}
                  disabled={isSaving}
                >
                  Reset to Default
                </button>
              </div>
            </div>

            <div className="theme-preview">
              <h3>Live Preview</h3>
              <div className="preview-card card">
                <h4>Sample Heading</h4>
                <p>This is how your text will look with the current theme settings. The colors and typography will update in real-time as you make changes.</p>
                <button className="btn btn-primary">Sample Button</button>

                <div className="preview-features">
                  <div className="preview-feature">
                    <div className="preview-feature-icon"></div>
                    <span>Primary color accent</span>
                  </div>
                  <div className="preview-feature">
                    <div className="preview-feature-icon"></div>
                    <span>Typography preview</span>
                  </div>
                  <div className="preview-feature">
                    <div className="preview-feature-icon"></div>
                    <span>Background contrast</span>
                  </div>
                </div>
              </div>

              <div className="theme-info">
                <h4>Current Theme Values</h4>
                <div className="theme-values">
                  <div className="theme-value">
                    <span className="label">Primary:</span>
                    <span className="value">{theme.primaryColor}</span>
                  </div>
                  <div className="theme-value">
                    <span className="label">Background:</span>
                    <span className="value">{theme.backgroundColor}</span>
                  </div>
                  <div className="theme-value">
                    <span className="label">Text:</span>
                    <span className="value">{theme.textColor}</span>
                  </div>
                  <div className="theme-value">
                    <span className="label">Font:</span>
                    <span className="value">{theme.fontFamily}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <style jsx>{`
        .loading-container {
          text-align: center;
          padding: var(--spacing-4xl);
        }

        .loading-container .loading {
          margin: 0 auto var(--spacing-lg);
        }

        .message {
          padding: var(--spacing-md) var(--spacing-lg);
          border-radius: var(--radius-md);
          margin-bottom: var(--spacing-xl);
          text-align: center;
          font-weight: var(--font-weight-medium);
        }

        .message.success {
          background-color: var(--success-color);
          color: white;
        }

        .message.error {
          background-color: var(--error-color);
          color: white;
        }

        .theme-content {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: var(--spacing-4xl);
          max-width: 1200px;
          margin: 0 auto;
        }

        .theme-form h3 {
          margin-bottom: var(--spacing-lg);
          color: var(--primary-color);
          border-bottom: 2px solid var(--primary-alpha);
          padding-bottom: var(--spacing-sm);
        }

        .form-group {
          margin-bottom: var(--spacing-lg);
        }

        .form-group label {
          display: block;
          margin-bottom: var(--spacing-sm);
          font-weight: var(--font-weight-medium);
          color: var(--text-color);
        }

        .form-group input[type="color"] {
          width: 100%;
          height: 50px;
          border: 2px solid var(--gray-300);
          border-radius: var(--radius-md);
          cursor: pointer;
          transition: border-color var(--transition-fast);
        }

        .form-group input[type="color"]:hover {
          border-color: var(--primary-color);
        }

        .color-value {
          display: block;
          margin-top: var(--spacing-xs);
          font-size: var(--font-size-sm);
          color: var(--gray-500);
          font-family: monospace;
        }

        .form-group select {
          width: 100%;
          padding: var(--spacing-md);
          border: 1px solid var(--gray-300);
          border-radius: var(--radius-md);
          font-size: var(--font-size-md);
          background-color: var(--background-color);
          color: var(--text-color);
          transition: border-color var(--transition-fast);
        }

        .form-group select:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px var(--primary-alpha);
        }

        .form-actions {
          display: flex;
          gap: var(--spacing-md);
          margin-top: var(--spacing-xl);
        }

        .btn-secondary {
          background-color: var(--gray-200);
          color: var(--gray-700);
          border: 1px solid var(--gray-300);
        }

        .btn-secondary:hover {
          background-color: var(--gray-300);
          color: var(--gray-800);
        }

        .btn:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }

        .theme-preview h3 {
          margin-bottom: var(--spacing-lg);
          color: var(--text-color);
        }

        .preview-card {
          padding: var(--spacing-2xl);
          border: 2px dashed var(--gray-300);
        }

        .preview-card h4 {
          margin-bottom: var(--spacing-md);
          color: var(--text-color);
        }

        .preview-card p {
          margin-bottom: var(--spacing-lg);
          color: var(--gray-600);
        }

        .preview-features {
          margin-top: var(--spacing-xl);
        }

        .preview-feature {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
          margin-bottom: var(--spacing-sm);
          padding: var(--spacing-sm);
          background-color: var(--primary-alpha);
          border-radius: var(--radius-sm);
        }

        .preview-feature-icon {
          width: 16px;
          height: 16px;
          background-color: var(--primary-color);
          border-radius: 50%;
        }

        .theme-info {
          margin-top: var(--spacing-xl);
          padding: var(--spacing-lg);
          background-color: var(--gray-50);
          border-radius: var(--radius-md);
        }

        .theme-info h4 {
          margin-bottom: var(--spacing-md);
          color: var(--text-color);
          font-size: var(--font-size-lg);
        }

        .theme-values {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-sm);
        }

        .theme-value {
          display: flex;
          justify-content: space-between;
          align-items: center;
          padding: var(--spacing-xs) 0;
          border-bottom: 1px solid var(--gray-200);
        }

        .theme-value:last-child {
          border-bottom: none;
        }

        .theme-value .label {
          font-weight: var(--font-weight-medium);
          color: var(--gray-600);
        }

        .theme-value .value {
          font-family: monospace;
          font-size: var(--font-size-sm);
          color: var(--text-color);
          background-color: var(--background-color);
          padding: var(--spacing-xs) var(--spacing-sm);
          border-radius: var(--radius-sm);
          border: 1px solid var(--gray-200);
        }

        @media (max-width: 768px) {
          .theme-content {
            grid-template-columns: 1fr;
            gap: var(--spacing-2xl);
          }

          .form-actions {
            flex-direction: column;
          }

          .theme-value {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--spacing-xs);
          }
        }
      `}</style>
    </div>
  );
};

export default ThemeSettings;
