const mongoose = require('mongoose');

const settingsSchema = new mongoose.Schema({
  theme: {
    primaryColor: { 
      type: String, 
      default: '#0066ff',
      required: true
    },
    backgroundColor: { 
      type: String, 
      default: '#ffffff',
      required: true
    },
    textColor: { 
      type: String, 
      default: '#222222',
      required: true
    },
    fontFamily: { 
      type: String, 
      default: 'Poppins',
      required: true
    },
    fontSize: {
      type: String,
      default: '16px',
      required: true
    }
  },
  siteInfo: {
    title: {
      type: String,
      default: 'My Portfolio',
      required: true
    },
    subtitle: {
      type: String,
      default: 'Web Developer & Designer'
    },
    description: {
      type: String,
      default: 'Welcome to my portfolio website'
    },
    email: {
      type: String,
      default: '<EMAIL>'
    },
    phone: {
      type: String,
      default: '+****************'
    }
  }
}, {
  timestamps: true
});

// Ensure only one settings document exists
settingsSchema.statics.getSettings = async function() {
  let settings = await this.findOne();
  if (!settings) {
    settings = await this.create({});
  }
  return settings;
};

settingsSchema.statics.updateSettings = async function(updateData) {
  let settings = await this.findOne();
  if (!settings) {
    settings = await this.create(updateData);
  } else {
    Object.assign(settings, updateData);
    await settings.save();
  }
  return settings;
};

module.exports = mongoose.model('Settings', settingsSchema);
