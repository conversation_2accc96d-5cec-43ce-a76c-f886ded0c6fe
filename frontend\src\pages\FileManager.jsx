import React, { useState } from 'react';
import FileUpload from '../components/FileUpload';

const FileManager = () => {
  const [activeTab, setActiveTab] = useState('images');
  const [uploadMessage, setUploadMessage] = useState('');

  const handleUploadSuccess = (data) => {
    const files = Array.isArray(data) ? data : [data];
    setUploadMessage(`Successfully uploaded ${files.length} file(s)`);
    setTimeout(() => setUploadMessage(''), 3000);
  };

  const handleUploadError = (error) => {
    setUploadMessage(`Upload error: ${error}`);
    setTimeout(() => setUploadMessage(''), 5000);
  };

  const tabs = [
    { id: 'images', label: 'Images', icon: '🖼️' },
    { id: 'resumes', label: 'Resumes', icon: '📄' },
    { id: 'documents', label: 'Documents', icon: '📁' }
  ];

  return (
    <div className="file-manager">
      <section className="section">
        <div className="container">
          <h1 className="section-title">File Manager</h1>
          <p className="section-subtitle">
            Upload and manage your portfolio files, images, and documents.
          </p>

          {uploadMessage && (
            <div className={`message ${uploadMessage.includes('error') ? 'error' : 'success'}`}>
              {uploadMessage}
            </div>
          )}

          <div className="file-tabs">
            {tabs.map(tab => (
              <button
                key={tab.id}
                className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
                onClick={() => setActiveTab(tab.id)}
              >
                <span className="tab-icon">{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>

          <div className="tab-content">
            {activeTab === 'images' && (
              <div className="upload-section">
                <h3>Image Upload</h3>
                <p className="section-description">
                  Upload images for your portfolio projects. Supported formats: JPEG, PNG, GIF, WebP
                </p>
                <FileUpload
                  type="image"
                  multiple={true}
                  onUploadSuccess={handleUploadSuccess}
                  onUploadError={handleUploadError}
                  maxSize={10 * 1024 * 1024} // 10MB
                />
              </div>
            )}

            {activeTab === 'resumes' && (
              <div className="upload-section">
                <h3>Resume Upload</h3>
                <p className="section-description">
                  Upload your resume or CV. Supported formats: PDF, DOC, DOCX
                </p>
                <FileUpload
                  type="resume"
                  multiple={false}
                  onUploadSuccess={handleUploadSuccess}
                  onUploadError={handleUploadError}
                  maxSize={5 * 1024 * 1024} // 5MB
                />
              </div>
            )}

            {activeTab === 'documents' && (
              <div className="upload-section">
                <h3>Document Upload</h3>
                <p className="section-description">
                  Upload other documents and files for your portfolio.
                </p>
                <FileUpload
                  type="document"
                  multiple={true}
                  onUploadSuccess={handleUploadSuccess}
                  onUploadError={handleUploadError}
                  accept="application/pdf,text/plain,.doc,.docx,.txt"
                  maxSize={10 * 1024 * 1024} // 10MB
                />
              </div>
            )}
          </div>

          <div className="upload-guidelines">
            <h3>Upload Guidelines</h3>
            <div className="guidelines-grid">
              <div className="guideline-card">
                <h4>Image Guidelines</h4>
                <ul>
                  <li>Maximum file size: 10MB</li>
                  <li>Recommended formats: JPEG, PNG</li>
                  <li>Optimal dimensions: 1920x1080 or higher</li>
                  <li>Use descriptive filenames</li>
                </ul>
              </div>

              <div className="guideline-card">
                <h4>Resume Guidelines</h4>
                <ul>
                  <li>Maximum file size: 5MB</li>
                  <li>Preferred format: PDF</li>
                  <li>Keep file size optimized</li>
                  <li>Use professional formatting</li>
                </ul>
              </div>

              <div className="guideline-card">
                <h4>General Tips</h4>
                <ul>
                  <li>Compress large files before uploading</li>
                  <li>Use meaningful file names</li>
                  <li>Organize files by project or category</li>
                  <li>Regularly clean up unused files</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </section>

      <style jsx>{`
        .message {
          padding: var(--spacing-md) var(--spacing-lg);
          border-radius: var(--radius-md);
          margin-bottom: var(--spacing-xl);
          text-align: center;
          font-weight: var(--font-weight-medium);
        }

        .message.success {
          background-color: var(--success-color);
          color: white;
        }

        .message.error {
          background-color: var(--error-color);
          color: white;
        }

        .file-tabs {
          display: flex;
          gap: var(--spacing-sm);
          margin-bottom: var(--spacing-2xl);
          border-bottom: 2px solid var(--gray-200);
        }

        .tab-button {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
          padding: var(--spacing-md) var(--spacing-lg);
          background: none;
          border: none;
          border-bottom: 3px solid transparent;
          cursor: pointer;
          font-size: var(--font-size-md);
          font-weight: var(--font-weight-medium);
          color: var(--gray-600);
          transition: all var(--transition-fast);
        }

        .tab-button:hover {
          color: var(--primary-color);
          background-color: var(--primary-alpha);
        }

        .tab-button.active {
          color: var(--primary-color);
          border-bottom-color: var(--primary-color);
        }

        .tab-icon {
          font-size: var(--font-size-lg);
        }

        .tab-content {
          margin-bottom: var(--spacing-4xl);
        }

        .upload-section {
          max-width: 800px;
          margin: 0 auto;
        }

        .upload-section h3 {
          margin-bottom: var(--spacing-md);
          color: var(--text-color);
        }

        .section-description {
          color: var(--gray-600);
          margin-bottom: var(--spacing-xl);
          line-height: var(--line-height-relaxed);
        }

        .upload-guidelines {
          max-width: 1000px;
          margin: 0 auto;
        }

        .upload-guidelines h3 {
          text-align: center;
          margin-bottom: var(--spacing-xl);
          color: var(--text-color);
        }

        .guidelines-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: var(--spacing-xl);
        }

        .guideline-card {
          background-color: var(--gray-50);
          padding: var(--spacing-xl);
          border-radius: var(--radius-lg);
          border: 1px solid var(--gray-200);
        }

        .guideline-card h4 {
          margin-bottom: var(--spacing-md);
          color: var(--primary-color);
          font-size: var(--font-size-lg);
        }

        .guideline-card ul {
          list-style: none;
          padding: 0;
          margin: 0;
        }

        .guideline-card li {
          padding: var(--spacing-xs) 0;
          color: var(--gray-600);
          position: relative;
          padding-left: var(--spacing-lg);
        }

        .guideline-card li::before {
          content: '✓';
          position: absolute;
          left: 0;
          color: var(--success-color);
          font-weight: var(--font-weight-bold);
        }

        @media (max-width: 768px) {
          .file-tabs {
            flex-direction: column;
            border-bottom: none;
          }

          .tab-button {
            justify-content: center;
            border-bottom: none;
            border-radius: var(--radius-md);
          }

          .tab-button.active {
            background-color: var(--primary-color);
            color: white;
          }

          .guidelines-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
};

export default FileManager;
