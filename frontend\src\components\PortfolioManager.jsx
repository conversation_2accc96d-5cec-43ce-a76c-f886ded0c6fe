import React, { useState, useEffect } from 'react';
import { portfolioAPI, uploadAPI } from '../utils/api';

const PortfolioManager = () => {
  const [portfolioItems, setPortfolioItems] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showForm, setShowForm] = useState(false);
  const [editingItem, setEditingItem] = useState(null);
  const [formData, setFormData] = useState({
    title: '',
    description: '',
    shortDescription: '',
    technologies: [],
    category: 'web',
    featured: false,
    links: {
      live: '',
      github: ''
    }
  });

  useEffect(() => {
    loadPortfolioItems();
  }, []);

  const loadPortfolioItems = async () => {
    try {
      const response = await portfolioAPI.getAll();
      if (response.success) {
        setPortfolioItems(response.data);
      }
    } catch (error) {
      console.error('Error loading portfolio items:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      if (editingItem) {
        await portfolioAPI.update(editingItem._id, formData);
      } else {
        await portfolioAPI.create(formData);
      }
      
      setShowForm(false);
      setEditingItem(null);
      resetForm();
      loadPortfolioItems();
    } catch (error) {
      console.error('Error saving portfolio item:', error);
    }
  };

  const handleEdit = (item) => {
    setEditingItem(item);
    setFormData({
      title: item.title,
      description: item.description,
      shortDescription: item.shortDescription || '',
      technologies: item.technologies || [],
      category: item.category,
      featured: item.featured,
      links: item.links || { live: '', github: '' }
    });
    setShowForm(true);
  };

  const handleDelete = async (id) => {
    if (window.confirm('Are you sure you want to delete this portfolio item?')) {
      try {
        await portfolioAPI.delete(id);
        loadPortfolioItems();
      } catch (error) {
        console.error('Error deleting portfolio item:', error);
      }
    }
  };

  const toggleFeatured = async (id) => {
    try {
      await portfolioAPI.toggleFeatured(id);
      loadPortfolioItems();
    } catch (error) {
      console.error('Error toggling featured status:', error);
    }
  };

  const resetForm = () => {
    setFormData({
      title: '',
      description: '',
      shortDescription: '',
      technologies: [],
      category: 'web',
      featured: false,
      links: {
        live: '',
        github: ''
      }
    });
  };

  const handleTechnologyAdd = (tech) => {
    if (tech && !formData.technologies.includes(tech)) {
      setFormData({
        ...formData,
        technologies: [...formData.technologies, tech]
      });
    }
  };

  const handleTechnologyRemove = (tech) => {
    setFormData({
      ...formData,
      technologies: formData.technologies.filter(t => t !== tech)
    });
  };

  if (isLoading) {
    return <div className="loading">Loading portfolio items...</div>;
  }

  return (
    <div className="portfolio-manager">
      <div className="manager-header">
        <h2>Portfolio Management</h2>
        <button 
          className="btn btn-primary"
          onClick={() => setShowForm(true)}
        >
          Add New Item
        </button>
      </div>

      {showForm && (
        <div className="form-modal">
          <div className="form-content">
            <h3>{editingItem ? 'Edit Portfolio Item' : 'Add New Portfolio Item'}</h3>
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label>Title</label>
                <input
                  type="text"
                  value={formData.title}
                  onChange={(e) => setFormData({...formData, title: e.target.value})}
                  required
                />
              </div>

              <div className="form-group">
                <label>Short Description</label>
                <input
                  type="text"
                  value={formData.shortDescription}
                  onChange={(e) => setFormData({...formData, shortDescription: e.target.value})}
                  maxLength="200"
                />
              </div>

              <div className="form-group">
                <label>Description</label>
                <textarea
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  rows="4"
                  required
                />
              </div>

              <div className="form-group">
                <label>Category</label>
                <select
                  value={formData.category}
                  onChange={(e) => setFormData({...formData, category: e.target.value})}
                >
                  <option value="web">Web</option>
                  <option value="mobile">Mobile</option>
                  <option value="desktop">Desktop</option>
                  <option value="design">Design</option>
                  <option value="other">Other</option>
                </select>
              </div>

              <div className="form-group">
                <label>Live URL</label>
                <input
                  type="url"
                  value={formData.links.live}
                  onChange={(e) => setFormData({
                    ...formData, 
                    links: {...formData.links, live: e.target.value}
                  })}
                />
              </div>

              <div className="form-group">
                <label>GitHub URL</label>
                <input
                  type="url"
                  value={formData.links.github}
                  onChange={(e) => setFormData({
                    ...formData, 
                    links: {...formData.links, github: e.target.value}
                  })}
                />
              </div>

              <div className="form-group">
                <label>
                  <input
                    type="checkbox"
                    checked={formData.featured}
                    onChange={(e) => setFormData({...formData, featured: e.target.checked})}
                  />
                  Featured Item
                </label>
              </div>

              <div className="form-actions">
                <button type="submit" className="btn btn-primary">
                  {editingItem ? 'Update' : 'Create'}
                </button>
                <button 
                  type="button" 
                  className="btn btn-secondary"
                  onClick={() => {
                    setShowForm(false);
                    setEditingItem(null);
                    resetForm();
                  }}
                >
                  Cancel
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className="portfolio-list">
        {portfolioItems.map(item => (
          <div key={item._id} className="portfolio-item-card">
            <div className="item-header">
              <h3>{item.title}</h3>
              <div className="item-actions">
                <button
                  className={`btn btn-sm ${item.featured ? 'btn-warning' : 'btn-outline'}`}
                  onClick={() => toggleFeatured(item._id)}
                >
                  {item.featured ? 'Unfeature' : 'Feature'}
                </button>
                <button
                  className="btn btn-sm btn-primary"
                  onClick={() => handleEdit(item)}
                >
                  Edit
                </button>
                <button
                  className="btn btn-sm btn-danger"
                  onClick={() => handleDelete(item._id)}
                >
                  Delete
                </button>
              </div>
            </div>
            <p className="item-description">{item.shortDescription || item.description}</p>
            <div className="item-meta">
              <span className="category">{item.category}</span>
              <span className="status">{item.status}</span>
              {item.featured && <span className="featured-badge">Featured</span>}
            </div>
          </div>
        ))}
      </div>

      <style jsx>{`
        .portfolio-manager {
          padding: var(--spacing-lg);
        }

        .manager-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: var(--spacing-xl);
        }

        .form-modal {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background-color: rgba(0, 0, 0, 0.5);
          display: flex;
          align-items: center;
          justify-content: center;
          z-index: var(--z-modal);
        }

        .form-content {
          background-color: var(--background-color);
          padding: var(--spacing-2xl);
          border-radius: var(--radius-lg);
          max-width: 600px;
          width: 90%;
          max-height: 90vh;
          overflow-y: auto;
        }

        .portfolio-list {
          display: grid;
          gap: var(--spacing-lg);
        }

        .portfolio-item-card {
          background-color: var(--background-color);
          border: 1px solid var(--gray-200);
          border-radius: var(--radius-lg);
          padding: var(--spacing-lg);
        }

        .item-header {
          display: flex;
          justify-content: space-between;
          align-items: flex-start;
          margin-bottom: var(--spacing-md);
        }

        .item-actions {
          display: flex;
          gap: var(--spacing-sm);
        }

        .btn-sm {
          padding: var(--spacing-xs) var(--spacing-sm);
          font-size: var(--font-size-sm);
        }

        .item-meta {
          display: flex;
          gap: var(--spacing-sm);
          margin-top: var(--spacing-md);
        }

        .category, .status, .featured-badge {
          padding: var(--spacing-xs) var(--spacing-sm);
          border-radius: var(--radius-sm);
          font-size: var(--font-size-xs);
          font-weight: var(--font-weight-medium);
        }

        .category {
          background-color: var(--primary-alpha);
          color: var(--primary-color);
        }

        .status {
          background-color: var(--gray-200);
          color: var(--gray-700);
        }

        .featured-badge {
          background-color: var(--warning-color);
          color: white;
        }
      `}</style>
    </div>
  );
};

export default PortfolioManager;
