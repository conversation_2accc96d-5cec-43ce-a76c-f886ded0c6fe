import React from 'react';
import { Link } from 'react-router-dom';

const Home = () => {
  return (
    <div className="home">
      {/* Hero Section */}
      <section className="hero">
        <div className="container">
          <div className="hero-content">
            <h1 className="hero-title fade-in">
              Welcome to My Portfolio
            </h1>
            <p className="hero-subtitle fade-in">
              I'm a passionate web developer creating amazing digital experiences
            </p>
            <div className="hero-buttons fade-in">
              <Link to="/portfolio" className="btn btn-primary">
                View My Work
              </Link>
              <Link to="/contact" className="btn btn-secondary">
                Get In Touch
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="features section">
        <div className="container">
          <h2 className="section-title">What I Do</h2>
          <div className="features-grid">
            <div className="feature-card card">
              <div className="feature-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <polyline points="16,18 22,12 16,6"></polyline>
                  <polyline points="8,6 2,12 8,18"></polyline>
                </svg>
              </div>
              <h3 className="feature-title">Web Development</h3>
              <p className="feature-description">
                Building responsive and modern web applications using the latest technologies.
              </p>
            </div>

            <div className="feature-card card">
              <div className="feature-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                  <line x1="8" y1="21" x2="16" y2="21"></line>
                  <line x1="12" y1="17" x2="12" y2="21"></line>
                </svg>
              </div>
              <h3 className="feature-title">UI/UX Design</h3>
              <p className="feature-description">
                Creating beautiful and intuitive user interfaces with great user experience.
              </p>
            </div>

            <div className="feature-card card">
              <div className="feature-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="10"></circle>
                  <polygon points="10,8 16,12 10,16 10,8"></polygon>
                </svg>
              </div>
              <h3 className="feature-title">Performance</h3>
              <p className="feature-description">
                Optimizing applications for speed, accessibility, and search engine visibility.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="cta section">
        <div className="container">
          <div className="cta-content">
            <h2 className="cta-title">Ready to Start Your Project?</h2>
            <p className="cta-description">
              Let's work together to bring your ideas to life with modern web technologies.
            </p>
            <Link to="/contact" className="btn btn-primary btn-large">
              Start a Project
            </Link>
          </div>
        </div>
      </section>

      <style jsx>{`
        .hero {
          min-height: 80vh;
          display: flex;
          align-items: center;
          background: linear-gradient(135deg, var(--primary-alpha) 0%, transparent 100%);
          position: relative;
          overflow: hidden;
        }

        .hero::before {
          content: '';
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="%23e5e7eb" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>');
          opacity: 0.1;
        }

        .hero-content {
          text-align: center;
          max-width: 800px;
          margin: 0 auto;
          position: relative;
          z-index: 1;
        }

        .hero-title {
          font-size: var(--font-size-5xl);
          font-weight: var(--font-weight-bold);
          margin-bottom: var(--spacing-lg);
          background: linear-gradient(135deg, var(--text-color), var(--primary-color));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .hero-subtitle {
          font-size: var(--font-size-xl);
          color: var(--gray-600);
          margin-bottom: var(--spacing-2xl);
          line-height: var(--line-height-relaxed);
        }

        .hero-buttons {
          display: flex;
          gap: var(--spacing-lg);
          justify-content: center;
          flex-wrap: wrap;
        }

        .btn-secondary {
          background-color: transparent;
          color: var(--text-color);
          border: 2px solid var(--gray-300);
        }

        .btn-secondary:hover {
          background-color: var(--text-color);
          color: var(--background-color);
          border-color: var(--text-color);
        }

        .btn-large {
          padding: var(--spacing-md) var(--spacing-2xl);
          font-size: var(--font-size-lg);
        }

        .features-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: var(--spacing-2xl);
        }

        .feature-card {
          text-align: center;
          padding: var(--spacing-2xl);
        }

        .feature-icon {
          color: var(--primary-color);
          margin-bottom: var(--spacing-lg);
          display: flex;
          justify-content: center;
        }

        .feature-title {
          font-size: var(--font-size-xl);
          font-weight: var(--font-weight-semibold);
          margin-bottom: var(--spacing-md);
          color: var(--text-color);
        }

        .feature-description {
          color: var(--gray-600);
          line-height: var(--line-height-relaxed);
        }

        .cta {
          background: linear-gradient(135deg, var(--primary-color), var(--primary-dark));
          color: white;
        }

        .cta-content {
          text-align: center;
          max-width: 600px;
          margin: 0 auto;
        }

        .cta-title {
          font-size: var(--font-size-3xl);
          font-weight: var(--font-weight-bold);
          margin-bottom: var(--spacing-lg);
          color: white;
        }

        .cta-description {
          font-size: var(--font-size-lg);
          margin-bottom: var(--spacing-2xl);
          opacity: 0.9;
          line-height: var(--line-height-relaxed);
        }

        .cta .btn-primary {
          background-color: white;
          color: var(--primary-color);
        }

        .cta .btn-primary:hover {
          background-color: var(--gray-100);
          transform: translateY(-2px);
        }

        @media (max-width: 768px) {
          .hero-title {
            font-size: var(--font-size-4xl);
          }

          .hero-subtitle {
            font-size: var(--font-size-lg);
          }

          .hero-buttons {
            flex-direction: column;
            align-items: center;
          }

          .features-grid {
            grid-template-columns: 1fr;
            gap: var(--spacing-xl);
          }

          .feature-card {
            padding: var(--spacing-xl);
          }

          .cta-title {
            font-size: var(--font-size-2xl);
          }
        }
      `}</style>
    </div>
  );
};

export default Home;
