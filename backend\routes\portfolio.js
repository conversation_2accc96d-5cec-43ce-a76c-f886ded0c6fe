const express = require('express');
const router = express.Router();
const { Portfolio } = require('../models');

// GET /api/portfolio - Get all portfolio items
router.get('/', async (req, res) => {
  try {
    const { status, category, featured } = req.query;
    
    // Build query
    let query = {};
    if (status) query.status = status;
    if (category) query.category = category;
    if (featured !== undefined) query.featured = featured === 'true';
    
    const portfolioItems = await Portfolio.find(query)
      .sort({ featured: -1, order: 1, createdAt: -1 });
    
    res.json({
      success: true,
      data: portfolioItems,
      count: portfolioItems.length
    });
  } catch (error) {
    console.error('Error fetching portfolio items:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching portfolio items',
      error: error.message
    });
  }
});

// GET /api/portfolio/featured - Get featured portfolio items
router.get('/featured', async (req, res) => {
  try {
    const featuredItems = await Portfolio.find({ 
      featured: true, 
      status: 'published' 
    }).sort({ order: 1, createdAt: -1 });
    
    res.json({
      success: true,
      data: featuredItems,
      count: featuredItems.length
    });
  } catch (error) {
    console.error('Error fetching featured portfolio items:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching featured portfolio items',
      error: error.message
    });
  }
});

// GET /api/portfolio/:id - Get single portfolio item
router.get('/:id', async (req, res) => {
  try {
    const portfolioItem = await Portfolio.findById(req.params.id);
    
    if (!portfolioItem) {
      return res.status(404).json({
        success: false,
        message: 'Portfolio item not found'
      });
    }
    
    res.json({
      success: true,
      data: portfolioItem
    });
  } catch (error) {
    console.error('Error fetching portfolio item:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching portfolio item',
      error: error.message
    });
  }
});

// POST /api/portfolio - Create new portfolio item
router.post('/', async (req, res) => {
  try {
    const portfolioData = req.body;
    const portfolioItem = new Portfolio(portfolioData);
    await portfolioItem.save();
    
    res.status(201).json({
      success: true,
      message: 'Portfolio item created successfully',
      data: portfolioItem
    });
  } catch (error) {
    console.error('Error creating portfolio item:', error);
    res.status(400).json({
      success: false,
      message: 'Error creating portfolio item',
      error: error.message
    });
  }
});

// PUT /api/portfolio/:id - Update portfolio item
router.put('/:id', async (req, res) => {
  try {
    const portfolioItem = await Portfolio.findByIdAndUpdate(
      req.params.id,
      req.body,
      { new: true, runValidators: true }
    );
    
    if (!portfolioItem) {
      return res.status(404).json({
        success: false,
        message: 'Portfolio item not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Portfolio item updated successfully',
      data: portfolioItem
    });
  } catch (error) {
    console.error('Error updating portfolio item:', error);
    res.status(400).json({
      success: false,
      message: 'Error updating portfolio item',
      error: error.message
    });
  }
});

// DELETE /api/portfolio/:id - Delete portfolio item
router.delete('/:id', async (req, res) => {
  try {
    const portfolioItem = await Portfolio.findByIdAndDelete(req.params.id);
    
    if (!portfolioItem) {
      return res.status(404).json({
        success: false,
        message: 'Portfolio item not found'
      });
    }
    
    res.json({
      success: true,
      message: 'Portfolio item deleted successfully'
    });
  } catch (error) {
    console.error('Error deleting portfolio item:', error);
    res.status(500).json({
      success: false,
      message: 'Error deleting portfolio item',
      error: error.message
    });
  }
});

// PATCH /api/portfolio/:id/toggle-featured - Toggle featured status
router.patch('/:id/toggle-featured', async (req, res) => {
  try {
    const portfolioItem = await Portfolio.findById(req.params.id);
    
    if (!portfolioItem) {
      return res.status(404).json({
        success: false,
        message: 'Portfolio item not found'
      });
    }
    
    portfolioItem.featured = !portfolioItem.featured;
    await portfolioItem.save();
    
    res.json({
      success: true,
      message: `Portfolio item ${portfolioItem.featured ? 'featured' : 'unfeatured'} successfully`,
      data: portfolioItem
    });
  } catch (error) {
    console.error('Error toggling featured status:', error);
    res.status(500).json({
      success: false,
      message: 'Error toggling featured status',
      error: error.message
    });
  }
});

module.exports = router;
