import React from 'react';

const About = () => {
  return (
    <div className="about">
      <section className="section">
        <div className="container">
          <h1 className="section-title">About Me</h1>
          <p className="section-subtitle">
            Learn more about my background, skills, and experience.
          </p>
          
          <div className="about-content">
            <div className="about-text">
              <h2>Hello, I'm a Web Developer</h2>
              <p>
                I'm passionate about creating beautiful, functional, and user-friendly websites 
                and applications. With expertise in modern web technologies, I help businesses 
                and individuals bring their digital visions to life.
              </p>
              <p>
                My journey in web development started several years ago, and I've been 
                continuously learning and adapting to new technologies and best practices 
                in the ever-evolving world of web development.
              </p>
            </div>
            
            <div className="skills-section">
              <h3>Skills & Technologies</h3>
              <div className="skills-grid">
                <div className="skill-item">React.js</div>
                <div className="skill-item">Node.js</div>
                <div className="skill-item">JavaScript</div>
                <div className="skill-item">CSS3</div>
                <div className="skill-item">HTML5</div>
                <div className="skill-item">MongoDB</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <style jsx>{`
        .about-content {
          max-width: 800px;
          margin: 0 auto;
          text-align: left;
        }

        .about-text {
          margin-bottom: var(--spacing-3xl);
        }

        .about-text h2 {
          margin-bottom: var(--spacing-lg);
          color: var(--primary-color);
        }

        .about-text p {
          font-size: var(--font-size-lg);
          line-height: var(--line-height-relaxed);
          color: var(--gray-600);
          margin-bottom: var(--spacing-lg);
        }

        .skills-section h3 {
          margin-bottom: var(--spacing-lg);
          color: var(--text-color);
        }

        .skills-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
          gap: var(--spacing-md);
        }

        .skill-item {
          background-color: var(--primary-alpha);
          color: var(--primary-color);
          padding: var(--spacing-sm) var(--spacing-md);
          border-radius: var(--radius-md);
          text-align: center;
          font-weight: var(--font-weight-medium);
        }
      `}</style>
    </div>
  );
};

export default About;
