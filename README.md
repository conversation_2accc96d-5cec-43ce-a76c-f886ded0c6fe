# Dynamic Portfolio Theme System

A complete, fully functional portfolio website with dynamic theme customization capabilities, built with React, Node.js, and MongoDB.

## 🚀 Features

- **🎨 Dynamic Theme System**: Real-time color and typography customization
- **📱 Responsive Design**: Mobile-first approach with modern CSS
- **🖼️ File Management**: Upload and manage images, resumes, and documents
- **⚡ Live Preview**: See theme changes instantly
- **🔧 Admin Panel**: Complete content management system
- **🎯 CSS Variables**: Easy theming with CSS custom properties
- **📊 Portfolio Management**: CRUD operations for portfolio items
- **🔒 File Validation**: Secure file upload with type and size validation

## 🛠️ Tech Stack

- **Frontend**: React.js + Vite + Custom CSS (CSS Variables)
- **Backend**: Node.js + Express.js + Multer
- **Database**: MongoDB + Mongoose
- **Styling**: Custom CSS with CSS Variables
- **File Upload**: Multer with organized storage

## 📁 Project Structure

```
Portfolio/
├── frontend/                   → React + Vite Application
│   ├── src/
│   │   ├── components/        → Reusable UI components
│   │   │   ├── Header.jsx     → Navigation header
│   │   │   ├── Footer.jsx     → Site footer
│   │   │   ├── LoadingSpinner.jsx
│   │   │   ├── FileUpload.jsx → File upload component
│   │   │   └── PortfolioManager.jsx
│   │   ├── pages/             → Route-level pages
│   │   │   ├── Home.jsx       → Landing page
│   │   │   ├── About.jsx      → About page
│   │   │   ├── Portfolio.jsx  → Portfolio showcase
│   │   │   ├── Contact.jsx    → Contact form
│   │   │   ├── AdminPanel.jsx → Admin dashboard
│   │   │   ├── ThemeSettings.jsx → Theme customization
│   │   │   └── FileManager.jsx → File management
│   │   ├── styles/            → CSS files
│   │   │   ├── variables.css  → CSS custom properties
│   │   │   └── global.css     → Global styles
│   │   ├── utils/             → Utilities
│   │   │   └── api.js         → API client & theme utils
│   │   ├── App.jsx            → Main app component
│   │   └── main.jsx           → App entry point
│   └── package.json
│
├── backend/                    → Node.js + Express API
│   ├── controllers/           → Business logic (ready for expansion)
│   ├── models/                → MongoDB schemas
│   │   ├── Settings.js        → Theme & site settings
│   │   ├── Portfolio.js       → Portfolio items
│   │   ├── User.js           → User management
│   │   └── index.js          → Model exports
│   ├── routes/                → API endpoints
│   │   ├── settings.js        → Settings CRUD
│   │   ├── portfolio.js       → Portfolio CRUD
│   │   └── upload.js          → File upload & management
│   ├── uploads/               → File storage
│   │   ├── images/           → Image uploads
│   │   ├── resumes/          → Resume uploads
│   │   └── documents/        → Other documents
│   ├── server.js             → Express server
│   ├── .env                  → Environment variables
│   └── package.json
│
├── test-api.js                → API testing script
└── README.md                 → This file
```

## 🚀 Quick Start

### Prerequisites
- Node.js (v16 or higher)
- MongoDB (local or cloud)
- Git

### 1. Clone & Setup
```bash
# Clone the repository
git clone <repository-url>
cd Portfolio

# Install backend dependencies
cd backend
npm install

# Install frontend dependencies
cd ../frontend
npm install
```

### 2. Environment Configuration
Create `backend/.env`:
```env
MONGODB_URI=mongodb://localhost:27017/portfolio
PORT=5000
NODE_ENV=development
```

### 3. Start the Application
```bash
# Terminal 1: Start Backend (from backend directory)
cd backend
npm run dev

# Terminal 2: Start Frontend (from frontend directory)
cd frontend
npm run dev
```

### 4. Access the Application
- **Portfolio**: http://localhost:5173
- **Admin Panel**: http://localhost:5173/admin
- **Theme Settings**: http://localhost:5173/admin/theme
- **File Manager**: http://localhost:5173/admin/files
- **API**: http://localhost:5000

## 🧪 Testing

Run the comprehensive API test:
```bash
# Install axios for testing
npm install axios

# Run API tests
node test-api.js
```

## 📚 API Documentation

### Theme Settings
```bash
GET    /api/settings/theme     # Get theme settings
PUT    /api/settings/theme     # Update theme settings
GET    /api/settings           # Get all settings
PUT    /api/settings           # Update all settings
```

### Portfolio Management
```bash
GET    /api/portfolio          # Get all portfolio items
GET    /api/portfolio/featured # Get featured items
GET    /api/portfolio/:id      # Get single item
POST   /api/portfolio          # Create new item
PUT    /api/portfolio/:id      # Update item
DELETE /api/portfolio/:id      # Delete item
PATCH  /api/portfolio/:id/toggle-featured # Toggle featured status
```

### File Upload
```bash
POST   /api/upload/image       # Upload single image
POST   /api/upload/images      # Upload multiple images
POST   /api/upload/resume      # Upload resume
GET    /api/upload/files       # List uploaded files
DELETE /api/upload/file/:filename # Delete file
```

## 🎨 Theme Customization

The system uses CSS custom properties for dynamic theming:

### Primary Theme Variables
```css
:root {
  --primary-color: #0066ff;      /* Main brand color */
  --background-color: #ffffff;   /* Page background */
  --text-color: #222222;         /* Main text color */
  --font-family: "Poppins";      /* Typography */
  --font-size-base: 16px;        /* Base font size */
}
```

### How It Works
1. **Admin Panel**: Change colors and fonts through the UI
2. **Real-time Updates**: CSS variables update instantly
3. **Persistent Storage**: Settings saved to MongoDB
4. **Auto-loading**: Theme loads automatically on page refresh

## 📱 Responsive Design

- **Mobile-first**: Optimized for mobile devices
- **Breakpoints**: 768px (tablet), 1024px (desktop)
- **Flexible Grid**: CSS Grid and Flexbox
- **Touch-friendly**: Large tap targets and gestures

## 🔒 File Upload Security

- **Type Validation**: Only allowed file types
- **Size Limits**: 10MB for images, 5MB for resumes
- **Organized Storage**: Files sorted by type
- **Secure Paths**: No direct file system access

## 🚀 Deployment

### Backend Deployment
1. Set production environment variables
2. Configure MongoDB connection
3. Set up file storage (local or cloud)
4. Deploy to your preferred platform

### Frontend Deployment
1. Build the production version: `npm run build`
2. Deploy the `dist` folder to your hosting service
3. Update API base URL for production

## 🔧 Customization

### Adding New Theme Options
1. Update `Settings.js` model
2. Add fields to `ThemeSettings.jsx`
3. Update CSS variables in `themeUtils.applyTheme()`

### Adding New File Types
1. Update file filter in `upload.js`
2. Add new upload methods to `uploadAPI`
3. Update `FileUpload.jsx` component

### Adding New Portfolio Fields
1. Update `Portfolio.js` model
2. Modify `PortfolioManager.jsx` form
3. Update display components

## 🐛 Troubleshooting

### Common Issues

**Backend won't start:**
- Check MongoDB connection
- Verify port 5000 is available
- Check environment variables

**Frontend won't start:**
- Verify port 5173 is available
- Check Node.js version
- Clear node_modules and reinstall

**File uploads fail:**
- Check upload directory permissions
- Verify file size and type
- Check backend logs

**Theme changes don't apply:**
- Check browser console for errors
- Verify API connection
- Clear browser cache

## 📄 License

This project is open source and available under the [MIT License](LICENSE).

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📞 Support

For questions or issues:
1. Check the troubleshooting section
2. Review the API documentation
3. Run the test script to verify setup
4. Check browser console and server logs

---

**Built with ❤️ using React, Node.js, and MongoDB**
