import React, { useState } from 'react';
import { Link, useLocation } from 'react-router-dom';

const Header = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const location = useLocation();

  const navItems = [
    { path: '/', label: 'Home' },
    { path: '/about', label: 'About' },
    { path: '/portfolio', label: 'Portfolio' },
    { path: '/contact', label: 'Contact' },
  ];

  const isActive = (path) => location.pathname === path;

  return (
    <header className="header">
      <nav className="navbar">
        <div className="container">
          <div className="nav-content">
            {/* Logo */}
            <Link to="/" className="logo">
              <span className="logo-text">Portfolio</span>
            </Link>

            {/* Desktop Navigation */}
            <div className="nav-links desktop-nav">
              {navItems.map((item) => (
                <Link
                  key={item.path}
                  to={item.path}
                  className={`nav-link ${isActive(item.path) ? 'active' : ''}`}
                >
                  {item.label}
                </Link>
              ))}
            </div>

            {/* Mobile Menu Button */}
            <button
              className="mobile-menu-btn"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              aria-label="Toggle menu"
            >
              <span className={`hamburger ${isMenuOpen ? 'open' : ''}`}>
                <span></span>
                <span></span>
                <span></span>
              </span>
            </button>
          </div>

          {/* Mobile Navigation */}
          <div className={`mobile-nav ${isMenuOpen ? 'open' : ''}`}>
            {navItems.map((item) => (
              <Link
                key={item.path}
                to={item.path}
                className={`mobile-nav-link ${isActive(item.path) ? 'active' : ''}`}
                onClick={() => setIsMenuOpen(false)}
              >
                {item.label}
              </Link>
            ))}
          </div>
        </div>
      </nav>

      <style jsx>{`
        .header {
          position: fixed;
          top: 0;
          left: 0;
          right: 0;
          z-index: var(--z-fixed);
          background-color: var(--background-color);
          box-shadow: var(--shadow-sm);
          backdrop-filter: blur(10px);
        }

        .navbar {
          height: var(--header-height);
        }

        .nav-content {
          display: flex;
          align-items: center;
          justify-content: space-between;
          height: 100%;
        }

        .logo {
          font-size: var(--font-size-xl);
          font-weight: var(--font-weight-bold);
          color: var(--primary-color);
          text-decoration: none;
        }

        .logo-text {
          background: linear-gradient(135deg, var(--primary-color), var(--primary-light));
          -webkit-background-clip: text;
          -webkit-text-fill-color: transparent;
          background-clip: text;
        }

        .desktop-nav {
          display: flex;
          gap: var(--spacing-lg);
        }

        .nav-link {
          color: var(--text-color);
          text-decoration: none;
          font-weight: var(--font-weight-medium);
          padding: var(--spacing-sm) var(--spacing-md);
          border-radius: var(--radius-md);
          transition: all var(--transition-fast);
          position: relative;
        }

        .nav-link:hover {
          color: var(--primary-color);
          background-color: var(--primary-alpha);
        }

        .nav-link.active {
          color: var(--primary-color);
        }

        .nav-link.active::after {
          content: '';
          position: absolute;
          bottom: -2px;
          left: 50%;
          transform: translateX(-50%);
          width: 20px;
          height: 2px;
          background-color: var(--primary-color);
          border-radius: 1px;
        }

        .mobile-menu-btn {
          display: none;
          background: none;
          border: none;
          cursor: pointer;
          padding: var(--spacing-sm);
        }

        .hamburger {
          display: flex;
          flex-direction: column;
          width: 24px;
          height: 18px;
          position: relative;
        }

        .hamburger span {
          display: block;
          height: 2px;
          width: 100%;
          background-color: var(--text-color);
          border-radius: 1px;
          transition: all var(--transition-fast);
        }

        .hamburger span:nth-child(1) {
          transform-origin: top left;
        }

        .hamburger span:nth-child(2) {
          margin: 6px 0;
        }

        .hamburger span:nth-child(3) {
          transform-origin: bottom left;
        }

        .hamburger.open span:nth-child(1) {
          transform: rotate(45deg) translate(2px, -2px);
        }

        .hamburger.open span:nth-child(2) {
          opacity: 0;
        }

        .hamburger.open span:nth-child(3) {
          transform: rotate(-45deg) translate(2px, 2px);
        }

        .mobile-nav {
          display: none;
          flex-direction: column;
          background-color: var(--background-color);
          border-top: 1px solid var(--gray-200);
          padding: var(--spacing-md) 0;
        }

        .mobile-nav.open {
          display: flex;
        }

        .mobile-nav-link {
          color: var(--text-color);
          text-decoration: none;
          font-weight: var(--font-weight-medium);
          padding: var(--spacing-md);
          border-radius: var(--radius-md);
          margin: 0 var(--spacing-md);
          transition: all var(--transition-fast);
        }

        .mobile-nav-link:hover,
        .mobile-nav-link.active {
          color: var(--primary-color);
          background-color: var(--primary-alpha);
        }

        @media (max-width: 768px) {
          .desktop-nav {
            display: none;
          }

          .mobile-menu-btn {
            display: block;
          }
        }
      `}</style>
    </header>
  );
};

export default Header;
