const mongoose = require('mongoose');

const portfolioSchema = new mongoose.Schema({
  title: {
    type: String,
    required: true,
    trim: true
  },
  description: {
    type: String,
    required: true
  },
  shortDescription: {
    type: String,
    maxlength: 200
  },
  technologies: [{
    type: String,
    trim: true
  }],
  images: [{
    filename: String,
    originalName: String,
    path: String,
    size: Number,
    mimetype: String
  }],
  links: {
    live: {
      type: String,
      validate: {
        validator: function(v) {
          return !v || /^https?:\/\/.+/.test(v);
        },
        message: 'Live URL must be a valid HTTP/HTTPS URL'
      }
    },
    github: {
      type: String,
      validate: {
        validator: function(v) {
          return !v || /^https?:\/\/.+/.test(v);
        },
        message: 'GitHub URL must be a valid HTTP/HTTPS URL'
      }
    }
  },
  category: {
    type: String,
    enum: ['web', 'mobile', 'desktop', 'design', 'other'],
    default: 'web'
  },
  featured: {
    type: Boolean,
    default: false
  },
  status: {
    type: String,
    enum: ['draft', 'published'],
    default: 'published'
  },
  order: {
    type: Number,
    default: 0
  }
}, {
  timestamps: true
});

// Index for better query performance
portfolioSchema.index({ status: 1, featured: -1, order: 1 });
portfolioSchema.index({ category: 1, status: 1 });

module.exports = mongoose.model('Portfolio', portfolioSchema);
