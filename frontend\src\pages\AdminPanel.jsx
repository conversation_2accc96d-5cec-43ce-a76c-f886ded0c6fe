import React from 'react';
import { Link } from 'react-router-dom';

const AdminPanel = () => {
  return (
    <div className="admin-panel">
      <section className="section">
        <div className="container">
          <h1 className="section-title">Admin Panel</h1>
          <p className="section-subtitle">
            Manage your portfolio content and settings.
          </p>
          
          <div className="admin-grid">
            <Link to="/admin/theme" className="admin-card card">
              <div className="admin-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <circle cx="12" cy="12" r="3"></circle>
                  <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m15.5-3.5L19 4l-1.5 1.5M6.5 17.5L5 19l1.5 1.5m0-15L5 5l1.5 1.5m11 11L19 19l-1.5-1.5"></path>
                </svg>
              </div>
              <h3>Theme Settings</h3>
              <p>Customize colors, fonts, and visual appearance of your portfolio.</p>
            </Link>

            <div className="admin-card card">
              <div className="admin-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                  <circle cx="9" cy="9" r="2"></circle>
                  <path d="M21 15l-3.086-3.086a2 2 0 00-2.828 0L6 21"></path>
                </svg>
              </div>
              <h3>Portfolio Items</h3>
              <p>Add, edit, and manage your portfolio projects and work samples.</p>
            </div>

            <div className="admin-card card">
              <div className="admin-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M14 2H6a2 2 0 00-2 2v16a2 2 0 002 2h12a2 2 0 002-2V8z"></path>
                  <polyline points="14,2 14,8 20,8"></polyline>
                  <line x1="16" y1="13" x2="8" y2="13"></line>
                  <line x1="16" y1="17" x2="8" y2="17"></line>
                  <polyline points="10,9 9,9 8,9"></polyline>
                </svg>
              </div>
              <h3>Content Management</h3>
              <p>Update your bio, contact information, and other site content.</p>
            </div>

            <Link to="/admin/files" className="admin-card card">
              <div className="admin-icon">
                <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21 16V8a2 2 0 00-1-1.73l-7-4a2 2 0 00-2 0l-7 4A2 2 0 003 8v8a2 2 0 001 1.73l7 4a2 2 0 002 0l7-4A2 2 0 0021 16z"></path>
                  <polyline points="3.27,6.96 12,12.01 20.73,6.96"></polyline>
                  <line x1="12" y1="22.08" x2="12" y2="12"></line>
                </svg>
              </div>
              <h3>File Manager</h3>
              <p>Upload and manage images, documents, and other media files.</p>
            </Link>
          </div>
        </div>
      </section>

      <style jsx>{`
        .admin-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
          gap: var(--spacing-2xl);
          max-width: 1000px;
          margin: 0 auto;
        }

        .admin-card {
          text-align: center;
          padding: var(--spacing-2xl);
          text-decoration: none;
          color: inherit;
          transition: all var(--transition-normal);
          border: 2px solid transparent;
        }

        .admin-card:hover {
          border-color: var(--primary-color);
          transform: translateY(-4px);
          box-shadow: var(--shadow-xl);
        }

        .admin-icon {
          color: var(--primary-color);
          margin-bottom: var(--spacing-lg);
          display: flex;
          justify-content: center;
        }

        .admin-card h3 {
          font-size: var(--font-size-xl);
          font-weight: var(--font-weight-semibold);
          margin-bottom: var(--spacing-md);
          color: var(--text-color);
        }

        .admin-card p {
          color: var(--gray-600);
          line-height: var(--line-height-relaxed);
        }

        @media (max-width: 768px) {
          .admin-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
};

export default AdminPanel;
