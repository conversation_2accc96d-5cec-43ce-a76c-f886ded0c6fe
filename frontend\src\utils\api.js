import axios from 'axios';

// Create axios instance with base configuration
const api = axios.create({
  baseURL: 'http://localhost:5000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor
api.interceptors.request.use(
  (config) => {
    // Add auth token if available
    const token = localStorage.getItem('authToken');
    if (token) {
      config.headers.Authorization = `Bear<PERSON> ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor
api.interceptors.response.use(
  (response) => {
    return response.data;
  },
  (error) => {
    // Handle common errors
    if (error.response) {
      // Server responded with error status
      const { status, data } = error.response;
      
      if (status === 401) {
        // Unauthorized - clear token and redirect to login
        localStorage.removeItem('authToken');
        window.location.href = '/admin/login';
      }
      
      // Return the error message from server
      return Promise.reject(data.message || 'An error occurred');
    } else if (error.request) {
      // Network error
      return Promise.reject('Network error. Please check your connection.');
    } else {
      // Other error
      return Promise.reject(error.message || 'An unexpected error occurred');
    }
  }
);

// Settings API
export const settingsAPI = {
  // Get all settings
  getSettings: () => api.get('/settings'),
  
  // Get theme settings only
  getTheme: () => api.get('/settings/theme'),
  
  // Update theme settings
  updateTheme: (themeData) => api.put('/settings/theme', { theme: themeData }),
  
  // Update site information
  updateSiteInfo: (siteData) => api.put('/settings/site', { siteInfo: siteData }),
  
  // Update all settings
  updateSettings: (settingsData) => api.put('/settings', settingsData),
};

// Portfolio API
export const portfolioAPI = {
  // Get all portfolio items
  getAll: (params = {}) => api.get('/portfolio', { params }),
  
  // Get featured portfolio items
  getFeatured: () => api.get('/portfolio/featured'),
  
  // Get single portfolio item
  getById: (id) => api.get(`/portfolio/${id}`),
  
  // Create new portfolio item
  create: (portfolioData) => api.post('/portfolio', portfolioData),
  
  // Update portfolio item
  update: (id, portfolioData) => api.put(`/portfolio/${id}`, portfolioData),
  
  // Delete portfolio item
  delete: (id) => api.delete(`/portfolio/${id}`),
  
  // Toggle featured status
  toggleFeatured: (id) => api.patch(`/portfolio/${id}/toggle-featured`),
};

// Upload API
export const uploadAPI = {
  // Upload single image
  uploadImage: (file) => {
    const formData = new FormData();
    formData.append('image', file);
    return api.post('/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Upload multiple images
  uploadImages: (files) => {
    const formData = new FormData();
    files.forEach(file => {
      formData.append('images', file);
    });
    return api.post('/upload/images', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // Upload resume
  uploadResume: (file) => {
    const formData = new FormData();
    formData.append('resume', file);
    return api.post('/upload/resume', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
  },

  // List uploaded files
  listFiles: (type) => api.get('/upload/files', { params: { type } }),

  // Delete uploaded file
  deleteFile: (filename, type) => api.delete(`/upload/file/${filename}`, { params: { type } }),
};

// Theme utility functions
export const themeUtils = {
  // Apply theme to CSS variables
  applyTheme: (theme) => {
    const root = document.documentElement;
    
    if (theme.primaryColor) {
      root.style.setProperty('--primary-color', theme.primaryColor);
      // Generate lighter and darker variants
      root.style.setProperty('--primary-light', lightenColor(theme.primaryColor, 20));
      root.style.setProperty('--primary-dark', darkenColor(theme.primaryColor, 20));
      root.style.setProperty('--primary-alpha', hexToRgba(theme.primaryColor, 0.1));
    }
    
    if (theme.backgroundColor) {
      root.style.setProperty('--background-color', theme.backgroundColor);
    }
    
    if (theme.textColor) {
      root.style.setProperty('--text-color', theme.textColor);
    }
    
    if (theme.fontFamily) {
      root.style.setProperty('--font-family', theme.fontFamily);
    }
    
    if (theme.fontSize) {
      root.style.setProperty('--font-size-base', theme.fontSize);
    }
  },
  
  // Load and apply theme from server
  loadTheme: async () => {
    try {
      const response = await settingsAPI.getTheme();
      if (response.success && response.data.theme) {
        themeUtils.applyTheme(response.data.theme);
        return response.data.theme;
      }
    } catch (error) {
      console.error('Error loading theme:', error);
    }
    return null;
  },
};

// Color utility functions
function hexToRgba(hex, alpha) {
  const r = parseInt(hex.slice(1, 3), 16);
  const g = parseInt(hex.slice(3, 5), 16);
  const b = parseInt(hex.slice(5, 7), 16);
  return `rgba(${r}, ${g}, ${b}, ${alpha})`;
}

function lightenColor(hex, percent) {
  const num = parseInt(hex.replace("#", ""), 16);
  const amt = Math.round(2.55 * percent);
  const R = (num >> 16) + amt;
  const G = (num >> 8 & 0x00FF) + amt;
  const B = (num & 0x0000FF) + amt;
  return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
    (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
    (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
}

function darkenColor(hex, percent) {
  const num = parseInt(hex.replace("#", ""), 16);
  const amt = Math.round(2.55 * percent);
  const R = (num >> 16) - amt;
  const G = (num >> 8 & 0x00FF) - amt;
  const B = (num & 0x0000FF) - amt;
  return "#" + (0x1000000 + (R > 255 ? 255 : R < 0 ? 0 : R) * 0x10000 +
    (G > 255 ? 255 : G < 0 ? 0 : G) * 0x100 +
    (B > 255 ? 255 : B < 0 ? 0 : B)).toString(16).slice(1);
}

export default api;
