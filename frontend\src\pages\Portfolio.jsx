import React from 'react';

const Portfolio = () => {
  return (
    <div className="portfolio">
      <section className="section">
        <div className="container">
          <h1 className="section-title">My Portfolio</h1>
          <p className="section-subtitle">
            Check out some of my recent projects and work.
          </p>
          
          <div className="portfolio-grid">
            <div className="portfolio-item card">
              <div className="portfolio-image">
                <div className="placeholder-image">Project Image</div>
              </div>
              <div className="portfolio-content">
                <h3>Sample Project 1</h3>
                <p>A description of this amazing project and the technologies used.</p>
                <div className="portfolio-tags">
                  <span className="tag">React</span>
                  <span className="tag">Node.js</span>
                </div>
              </div>
            </div>

            <div className="portfolio-item card">
              <div className="portfolio-image">
                <div className="placeholder-image">Project Image</div>
              </div>
              <div className="portfolio-content">
                <h3>Sample Project 2</h3>
                <p>Another great project showcasing different skills and technologies.</p>
                <div className="portfolio-tags">
                  <span className="tag">JavaScript</span>
                  <span className="tag">CSS</span>
                </div>
              </div>
            </div>

            <div className="portfolio-item card">
              <div className="portfolio-image">
                <div className="placeholder-image">Project Image</div>
              </div>
              <div className="portfolio-content">
                <h3>Sample Project 3</h3>
                <p>A third project demonstrating versatility and expertise.</p>
                <div className="portfolio-tags">
                  <span className="tag">MongoDB</span>
                  <span className="tag">Express</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      <style jsx>{`
        .portfolio-grid {
          display: grid;
          grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
          gap: var(--spacing-2xl);
        }

        .portfolio-item {
          overflow: hidden;
        }

        .portfolio-image {
          height: 200px;
          overflow: hidden;
          border-radius: var(--radius-lg) var(--radius-lg) 0 0;
        }

        .placeholder-image {
          width: 100%;
          height: 100%;
          background: linear-gradient(135deg, var(--primary-alpha), var(--gray-100));
          display: flex;
          align-items: center;
          justify-content: center;
          color: var(--gray-500);
          font-weight: var(--font-weight-medium);
        }

        .portfolio-content {
          padding: var(--spacing-lg);
        }

        .portfolio-content h3 {
          margin-bottom: var(--spacing-sm);
          color: var(--text-color);
        }

        .portfolio-content p {
          color: var(--gray-600);
          margin-bottom: var(--spacing-md);
          line-height: var(--line-height-relaxed);
        }

        .portfolio-tags {
          display: flex;
          gap: var(--spacing-sm);
          flex-wrap: wrap;
        }

        .tag {
          background-color: var(--primary-alpha);
          color: var(--primary-color);
          padding: var(--spacing-xs) var(--spacing-sm);
          border-radius: var(--radius-sm);
          font-size: var(--font-size-sm);
          font-weight: var(--font-weight-medium);
        }

        @media (max-width: 768px) {
          .portfolio-grid {
            grid-template-columns: 1fr;
          }
        }
      `}</style>
    </div>
  );
};

export default Portfolio;
