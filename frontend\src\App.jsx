import { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import { themeUtils } from './utils/api';
import './styles/global.css';

// Import components (will be created)
import Header from './components/Header';
import Footer from './components/Footer';
import LoadingSpinner from './components/LoadingSpinner';

// Import pages (will be created)
import Home from './pages/Home';
import About from './pages/About';
import Portfolio from './pages/Portfolio';
import Contact from './pages/Contact';
import AdminPanel from './pages/AdminPanel';
import ThemeSettings from './pages/ThemeSettings';
import FileManager from './pages/FileManager';

function App() {
  const [isLoading, setIsLoading] = useState(true);
  const [theme, setTheme] = useState(null);

  useEffect(() => {
    // Load theme on app initialization
    const loadInitialTheme = async () => {
      try {
        const loadedTheme = await themeUtils.loadTheme();
        setTheme(loadedTheme);
      } catch (error) {
        console.error('Error loading initial theme:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadInitialTheme();
  }, []);

  if (isLoading) {
    return <LoadingSpinner />;
  }

  return (
    <Router>
      <div className="App">
        <Header />
        <main className="main-content">
          <Routes>
            <Route path="/" element={<Home />} />
            <Route path="/about" element={<About />} />
            <Route path="/portfolio" element={<Portfolio />} />
            <Route path="/contact" element={<Contact />} />
            <Route path="/admin" element={<AdminPanel />} />
            <Route path="/admin/theme" element={<ThemeSettings />} />
            <Route path="/admin/files" element={<FileManager />} />
          </Routes>
        </main>
        <Footer />
      </div>
    </Router>
  );
}

export default App;
