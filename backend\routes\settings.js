const express = require('express');
const router = express.Router();
const { Settings } = require('../models');

// GET /api/settings - Get all settings
router.get('/', async (req, res) => {
  try {
    const settings = await Settings.getSettings();
    res.json({
      success: true,
      data: settings
    });
  } catch (error) {
    console.error('Error fetching settings:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching settings',
      error: error.message
    });
  }
});

// GET /api/settings/theme - Get theme settings only
router.get('/theme', async (req, res) => {
  try {
    const settings = await Settings.getSettings();
    res.json({
      success: true,
      data: {
        theme: settings.theme
      }
    });
  } catch (error) {
    console.error('Error fetching theme settings:', error);
    res.status(500).json({
      success: false,
      message: 'Error fetching theme settings',
      error: error.message
    });
  }
});

// PUT /api/settings/theme - Update theme settings
router.put('/theme', async (req, res) => {
  try {
    const { theme } = req.body;
    
    if (!theme) {
      return res.status(400).json({
        success: false,
        message: 'Theme data is required'
      });
    }

    const settings = await Settings.updateSettings({ theme });
    
    res.json({
      success: true,
      message: 'Theme settings updated successfully',
      data: {
        theme: settings.theme
      }
    });
  } catch (error) {
    console.error('Error updating theme settings:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating theme settings',
      error: error.message
    });
  }
});

// PUT /api/settings/site - Update site information
router.put('/site', async (req, res) => {
  try {
    const { siteInfo } = req.body;
    
    if (!siteInfo) {
      return res.status(400).json({
        success: false,
        message: 'Site information is required'
      });
    }

    const settings = await Settings.updateSettings({ siteInfo });
    
    res.json({
      success: true,
      message: 'Site information updated successfully',
      data: {
        siteInfo: settings.siteInfo
      }
    });
  } catch (error) {
    console.error('Error updating site information:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating site information',
      error: error.message
    });
  }
});

// PUT /api/settings - Update all settings
router.put('/', async (req, res) => {
  try {
    const updateData = req.body;
    const settings = await Settings.updateSettings(updateData);
    
    res.json({
      success: true,
      message: 'Settings updated successfully',
      data: settings
    });
  } catch (error) {
    console.error('Error updating settings:', error);
    res.status(500).json({
      success: false,
      message: 'Error updating settings',
      error: error.message
    });
  }
});

module.exports = router;
