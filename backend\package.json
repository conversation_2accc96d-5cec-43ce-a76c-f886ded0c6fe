{"name": "backend", "version": "1.0.0", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"cors": "^2.8.5", "dotenv": "^17.2.0", "express": "^4.21.2", "mongoose": "^8.16.3", "multer": "^2.0.1"}, "devDependencies": {"nodemon": "^3.1.10"}}