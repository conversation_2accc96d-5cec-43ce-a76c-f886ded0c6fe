import React from 'react';

const Contact = () => {
  return (
    <div className="contact">
      <section className="section">
        <div className="container">
          <h1 className="section-title">Get In Touch</h1>
          <p className="section-subtitle">
            Let's discuss your project and how we can work together.
          </p>
          
          <div className="contact-content">
            <div className="contact-info">
              <h3>Contact Information</h3>
              <div className="contact-item">
                <strong>Email:</strong> <EMAIL>
              </div>
              <div className="contact-item">
                <strong>Phone:</strong> +****************
              </div>
              <div className="contact-item">
                <strong>Location:</strong> Your City, Country
              </div>
            </div>

            <form className="contact-form">
              <div className="form-group">
                <label htmlFor="name">Name</label>
                <input type="text" id="name" name="name" required />
              </div>
              
              <div className="form-group">
                <label htmlFor="email">Email</label>
                <input type="email" id="email" name="email" required />
              </div>
              
              <div className="form-group">
                <label htmlFor="subject">Subject</label>
                <input type="text" id="subject" name="subject" required />
              </div>
              
              <div className="form-group">
                <label htmlFor="message">Message</label>
                <textarea id="message" name="message" rows="5" required></textarea>
              </div>
              
              <button type="submit" className="btn btn-primary">
                Send Message
              </button>
            </form>
          </div>
        </div>
      </section>

      <style jsx>{`
        .contact-content {
          display: grid;
          grid-template-columns: 1fr 2fr;
          gap: var(--spacing-4xl);
          max-width: 1000px;
          margin: 0 auto;
        }

        .contact-info h3 {
          margin-bottom: var(--spacing-lg);
          color: var(--primary-color);
        }

        .contact-item {
          margin-bottom: var(--spacing-md);
          color: var(--gray-600);
        }

        .contact-item strong {
          color: var(--text-color);
        }

        .contact-form {
          background-color: var(--gray-50);
          padding: var(--spacing-2xl);
          border-radius: var(--radius-lg);
        }

        .form-group {
          margin-bottom: var(--spacing-lg);
        }

        .form-group label {
          display: block;
          margin-bottom: var(--spacing-sm);
          font-weight: var(--font-weight-medium);
          color: var(--text-color);
        }

        .form-group input,
        .form-group textarea {
          width: 100%;
          padding: var(--spacing-md);
          border: 1px solid var(--gray-300);
          border-radius: var(--radius-md);
          font-size: var(--font-size-md);
          transition: border-color var(--transition-fast);
        }

        .form-group input:focus,
        .form-group textarea:focus {
          outline: none;
          border-color: var(--primary-color);
          box-shadow: 0 0 0 3px var(--primary-alpha);
        }

        .form-group textarea {
          resize: vertical;
          min-height: 120px;
        }

        @media (max-width: 768px) {
          .contact-content {
            grid-template-columns: 1fr;
            gap: var(--spacing-2xl);
          }

          .contact-form {
            padding: var(--spacing-xl);
          }
        }
      `}</style>
    </div>
  );
};

export default Contact;
